import { useMutation } from "@tanstack/react-query";
import { jsPDF } from "jspdf";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";

import { formatDecimal } from "@shared/utils/value-formatters.ts";

import { mergeGlobalStyles } from "./pdf/pdf-utils/global-styles.ts";
import { PdfCursorMarginSize } from "./pdf/pdf-utils/pdf-cursor.ts";
import { useGetBuildingReviewSections } from "./use-building-description-attributes.ts";
import { useOrgLogoPdfImage } from "./use-org-logo-pdf-image.ts";
import { useFetchPdfData } from "./use-pdf-data.ts";
import { usePdfGlobalStyles } from "./use-pdf-global-styles.ts";
import { usePdfLabels } from "./use-pdf-labels.ts";
import { useGetPdfPaintMethods } from "./use-pdf-paint-methods.ts";

export const useSummaryPdfExport = () => {
  const { i18n } = useTranslation();
  const pdfGlobalStyles = usePdfGlobalStyles();
  mergeGlobalStyles(pdfGlobalStyles);

  const getPaintMethods = useGetPdfPaintMethods();
  const convertPdfImages = useOrgLogoPdfImage();
  const fetchPdfData = useFetchPdfData();
  const getBuildingAttributes = useGetBuildingReviewSections();
  const labels = usePdfLabels();

  const generatePdf = useCallback(
    async (buildingId: string) => {
      const logo = await convertPdfImages();
      const {
        address,
        building,
        currentResult,
        newResult,
        measures,
        profitability,
      } = await fetchPdfData(buildingId);

      const buildingAttributes = await getBuildingAttributes(building);

      // TODO: below code goes to worker in the future
      const doc = new jsPDF();
      const initializedLabels = labels(currentResult);
      const paint = getPaintMethods(doc);
      const MAX_RENOVATIONS = 12;

      paint.disclaimer({ title: initializedLabels.disclaimer });

      paint.headerSection(address, logo);
      paint.cursor.addYMargin(PdfCursorMarginSize.SM);

      paint.separator();
      paint.cursor.addYMargin(PdfCursorMarginSize.SM);

      paint.title({
        ...initializedLabels.currentResultScale,
        ...initializedLabels.scale,
        value:
          formatDecimal(i18n.language, currentResult.primaryEnergyDemand) || "",
      });

      paint.scale({
        indicatorValue: currentResult?.primaryEnergyDemand,
      });

      paint.separator();
      paint.cursor.addYMargin(PdfCursorMarginSize.XS);
      paint.title({ title: initializedLabels.renovationsTitle });
      paint.cursor.addYMargin(PdfCursorMarginSize.XS);

      paint.renovationSection(
        measures,
        initializedLabels.renovations,
        MAX_RENOVATIONS
      );
      paint.cursor.addYMargin(PdfCursorMarginSize.XS);

      const newResultEnergyValueText =
        formatDecimal(i18n.language, newResult?.primaryEnergyDemand) ||
        initializedLabels.notAvailable;

      paint.title({
        ...initializedLabels.newResultScale,
        ...initializedLabels.scale,
        value: newResultEnergyValueText,
      });

      paint.scale({
        indicatorValue: newResult?.primaryEnergyDemand,
      });
      paint.cursor.addYMargin(PdfCursorMarginSize.XS);

      paint.title({ title: initializedLabels.profitabilityTitle });
      paint.cursor.addYMargin(PdfCursorMarginSize.XS);

      paint.profitabilitySection(
        newResult || currentResult,
        profitability,
        initializedLabels.profitability
      );

      paint.cursor.nextPage();

      paint.disclaimer({ title: initializedLabels.disclaimer });

      paint.headerSection(address, logo);
      paint.cursor.addYMargin(PdfCursorMarginSize.SM);
      paint.separator();
      paint.cursor.addYMargin(PdfCursorMarginSize.XS);

      paint.title({
        ...initializedLabels.buildingOverview,
      });
      paint.cursor.addYMargin(PdfCursorMarginSize.XS);

      buildingAttributes.forEach((section, index) => {
        if (index !== 0) paint.separator();

        paint.buildingSection(section);
      });

      doc.save(`EffizientPilot Export ${address}.pdf`);
    },
    [
      labels,
      i18n,
      getPaintMethods,
      getBuildingAttributes,
      convertPdfImages,
      fetchPdfData,
    ]
  );

  return useMutation({
    mutationFn: (buildingId: string) => generatePdf(buildingId),
  });
};
