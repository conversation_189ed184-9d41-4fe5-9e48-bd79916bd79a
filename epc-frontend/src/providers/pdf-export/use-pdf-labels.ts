import { useCallback } from "react";
import { useTranslation } from "react-i18next";

import { CalculationResultDto } from "@data-access/calculation/calculation-dto.ts";

export const usePdfLabels = () => {
  const { t } = useTranslation();

  // NOTE: the below object is created to avoid creating dependency between web worker and translations
  return useCallback(
    (currentResult: CalculationResultDto) => ({
      notAvailable: t("pdf.notAvailable"),
      renovationsTitle: t("pdf.selectedMeasures"),
      renovations: {
        price: t("pdf.price"),
        measure: t("pdf.measure"),
        noMeasuresSelected: t("pdf.noMeasuresSelected"),
        moreRenovations: t("pdf.moreRenovations"),
        grant: t("pdf.grant"),
      },
      scale: {
        unit: t("core.units.energyDemand"),
      },
      disclaimer: t("pdf.disclaimer"),
      currentResultScale: {
        title: t("pdf.currentPrimaryEnergyDemand"),
      },
      newResultScale: {
        title: t("pdf.alternativePrimaryEnergyDemand"),
      },
      profitabilityTitle: t("pdf.profitability"),
      profitability: {
        co2emission: t("pdf.co2Emission"),
        co2emissionUnit: t(`core.units.emission.${currentResult.co2EmissionsUnit}`),
        cost: t("pdf.renovationCost"),
        demandUnit: t("core.units.energyDemand"),
        energyCostSaved: t("pdf.energyCostSaved"),
        primaryEnergyDemand: t("pdf.primaryEnergyDemand"),
        finalEnergyDemand: t("pdf.finalEnergyDemandPerArea"),
        propertyValueIncrease: t("pdf.propertyValueIncrease"),
        solarProduction: t("pdf.solarEnergyProd"),
        totalProfit: t("pdf.totalProfit"),
        solarProductionUnit: t("core.units.energyPerAnnum"),
      },
      buildingOverview: { title: t("pdf.buildingOverview") },
    }),
    [t],
  );
};
