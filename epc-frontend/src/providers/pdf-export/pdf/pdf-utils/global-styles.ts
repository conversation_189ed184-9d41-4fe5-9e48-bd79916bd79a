import { jsPDF } from "jspdf";
import { merge } from "lodash";

import { DeepPartial } from "@shared/types/common.ts";

export type PdfGlobalStyles = {
  font: string;
  lines: {
    lg: number;
    md: number;
    sm: number;
  };
  fontSizes: {
    xl: number;
    lg: number;
    md: number;
    sm: number;
    xs: number;
  };
  colors: {
    greenText: string;
    basicText: string;
    basicDraw: string;
    textGray: string;
    textLightGray: string;
    lineGray: string;
  };
};

let globalStyles: PdfGlobalStyles = {
  font: "helvetica",
  lines: {
    lg: 1,
    md: 0.5,
    sm: 0.25,
  },
  fontSizes: {
    xl: 20,
    lg: 14,
    md: 12,
    sm: 10,
    xs: 8,
  },
  colors: {
    greenText: "#008000",
    basicText: "#000000",
    basicDraw: "#000000",
    textGray: "#808080",
    textLightGray: "#808080",
    lineGray: "#808080",
  },
};

export const getGlobalStyles = () => {
  return globalStyles;
};

export const mergeGlobalStyles = (styles: DeepPartial<PdfGlobalStyles>) => {
  globalStyles = merge(globalStyles, styles);
};

export const resetGlobalStyles = (doc: jsPDF) => {
  doc.setFont(globalStyles.font, "normal");
  doc.setFontSize(globalStyles.fontSizes.md);
  doc.setTextColor(globalStyles.colors.basicText);

  doc.setLineWidth(globalStyles.lines.md);
  doc.setDrawColor(globalStyles.colors.basicDraw);
};
