import { jsPDF } from "jspdf";

import { PdfCursor } from "@providers/pdf-export/pdf/pdf-utils/pdf-cursor.ts";

import {
  startX,
} from "../../pdf-utils/global-sizes.ts";
import {
  getGlobalStyles,
  resetGlobalStyles,
} from "../../pdf-utils/global-styles.ts";

type Texts = {
  title: string;
  value?: string;
  unit?: string;
};

const MARGIN_Y = 1;

export const preparePaintDisclaimer =
  (doc: jsPDF, cursor: PdfCursor) =>
    ({ title }: Texts) => {
      const y = cursor.getY();
      const {
        font,
        colors: { textLightGray },
        fontSizes: { xs },
      } = getGlobalStyles();

      doc.setFont(font);
      doc.setFontSize(xs);
      const { h: lineHeight } = doc.getTextDimensions(title);
      const textY = y + lineHeight + MARGIN_Y;

      doc.setTextColor(textLightGray);
      doc.text(title, startX(), textY);

      resetGlobalStyles(doc);
      cursor.replaceY(textY + MARGIN_Y);
    };
