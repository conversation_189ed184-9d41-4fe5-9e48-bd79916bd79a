import { jsPDF } from "jspdf";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";

import { useScaleConfig } from "@shared/components/scale/use-scale-config.ts";

import { preparePaintHeaderSection } from "./pdf/header-pdf-section.ts";
import { preparePaintBuildingDescriptionSection } from "./pdf/pdf-components/building-section/building-section.ts";
import { preparePaintEpcScale } from "./pdf/pdf-components/scale/scale.ts";
import { preparePaintSeparator } from "./pdf/pdf-components/separator/separator.ts";
import { preparePaintTitle } from "./pdf/pdf-components/title-pane/title-pane.ts";
import { createPdfCursor } from "./pdf/pdf-utils/pdf-cursor.ts";
import { preparePaintProfitabilitySection } from "./pdf/profitability-pdf-section.ts";
import { preparePaintRenovationListSection } from "./pdf/renovation-list-pdf-section.ts";
import { preparePaintDisclaimer } from "./pdf/pdf-components/disclaimer/disclaimer.ts";

export const useGetPdfPaintMethods = () => {
  const scaleConfig = useScaleConfig();
  const { i18n } = useTranslation();

  return useCallback(
    (doc: jsPDF) => {
      const pdfCursor = createPdfCursor(doc);
      const paintHeaderPdfSection = preparePaintHeaderSection(doc, pdfCursor);
      const paintProfitabilitySection = preparePaintProfitabilitySection(
        doc,
        pdfCursor,
        i18n.language
      );
      const paintRenovationSection = preparePaintRenovationListSection(
        doc,
        pdfCursor
      );

      const paintSeparator = preparePaintSeparator(doc, pdfCursor);
      const paintTitlePane = preparePaintTitle(doc, pdfCursor);
      const paintScale = preparePaintEpcScale(doc, pdfCursor, scaleConfig);
      const paintBuildingSection = preparePaintBuildingDescriptionSection(
        doc,
        pdfCursor
      );

      const paintDisclaimer = preparePaintDisclaimer(doc, pdfCursor);

      return {
        headerSection: paintHeaderPdfSection,
        profitabilitySection: paintProfitabilitySection,
        renovationSection: paintRenovationSection,
        separator: paintSeparator,
        title: paintTitlePane,
        disclaimer: paintDisclaimer,
        scale: paintScale,
        buildingSection: paintBuildingSection,
        cursor: pdfCursor,
      };
    },
    [scaleConfig, i18n.language]
  );
};
