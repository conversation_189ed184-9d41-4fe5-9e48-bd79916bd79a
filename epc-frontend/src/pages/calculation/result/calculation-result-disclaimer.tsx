import { useTranslation } from "react-i18next";
import { Group, Text, useMantineTheme } from "@mantine/core";
import { IconExclamationCircle } from "@tabler/icons-react";

export const CalculationResultDisclaimer = () => {
    const { t } = useTranslation();
    const theme = useMantineTheme();
    return (
        <Group gap="4" align="center" mt="xs">
            <IconExclamationCircle color={theme.colors.gray[6]} size="20" stroke="1.5" />
            <Text fz="xs" c="gray.6">
                {t("pages.calculationResult.disclaimer")}
            </Text>
        </Group>
    )
}