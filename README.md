## Finacte EPC

### Prerequisites

- JDK 17 (eclipse-temurin:17-jre-alpine is used in runtime) - for BE application development
- Node v18.16.0 - for FE application development
- AWS CDK CLI (typescript https://docs.aws.amazon.com/cdk/v2/guide/work-with-cdk-typescript.html ) - for AWS
  infrastructure development
- Docker (as new as possible, possibly 26.*)
- Docker Compose (as new as possible, possibly 2.*)
- API is described in https://finacte.atlassian.net/wiki/spaces/MFS/pages/6160406/Calculation+API and should be kept
  updated

#### Code style and linters
  - For Kotlin development we are using Kotlin official code style. This style should be set in Intellij Idea settings or [Ktlint](https://github.com/pinterest/ktlint#installation) should be installed together with `cstef.kotlin-formatter` in case VS Code is used. 
  - For Type Script development - TODO

### Building and running application (BE + FE)

The Maven application build process performs the following actions:

- FE production build is triggered
- FE production artifact is copied to `epc-backend/src/resources/public`
- BE is build

There are multiple ways of deploying application:

1. JAR File
   To build the whole project you need to run `./mvnw clean install -Ppackaged` in the root directory.
   This will create `./epc-backend/target/epc-backend-0.0.1-SNAPSHOT.jar`. You can run it
   using `java -DOPENAI_API_KEY=XXXXXXXXX -jar ./epc-backend/target/epc-backend-0.0.1-SNAPSHOT.jar`. (Additional info
   about profile below)

2. Spring Boot Maven Plugin
   You can run application using spring maven plugin by using `./mvnw spring-boot:run -Ppackaged` in the root
   directory. (Additional info about profile below)
3. Docker image
   You can build application using JIB Maven Plugin. If you would like to also push your image to remote
   repository please use instructions from [section related to docker push](#Pushing-Docker-image-to-AWS-ECR)

To run this image you can use [docker-compose](/epc-infra/docker/docker-compose.yaml):

```bash
cd epc-infra/docker
docker compose up
```

**Bear in mind that you must export your Open AI key into env variables using any of this method of deployment e.x.**
`export OPENAI_API_KEY=<INSERT YOUR KEY HERE>`

### BE Development run

You must have running Postgresql DB. You should use provided [docker-compose](/epc-infra/docker/docker-compose.yaml)
configuration and run it using:

```bash
docker-compose up epc-postgres
```

To run application you can just run `EpcApplication` main class or use Spring Boot plugin by running
`./mvnw spring-boot:run -Ppackaged`. Application is running on 8098 port. **Take into account that BE app has FE app in
it in the last build version. You can use it or run FE separately.**

#### Spring Boot Profiles

You can use the following profiles when running the application:

- `no-security` - security will be turned OFF. Please take into account that we are taking some data from the JWT token
  which
  in this case will not be available so some default value will be used.
- `local` - Check the `application-local.yaml` file for details.

You can use one or multiple profiles by adding the following parameter to the run command:

If you are using Spring Boot plugin run command you should use `-Dspring-boot.run.profiles=no-security,local` for
example `./mvnw spring-boot:run -Ppackaged -Dspring-boot.run.profiles=no-security,local`

If you are using JAR to run the application you should use `-Dspring.profiles.active=no-security,local` for example
`java -DOPENAI_API_KEY=XXXXXXXXX -Dspring.profiles.active=no-security,local -jar ./epc-backend/target/epc-backend-0.0.1-SNAPSHOT.jar`

### FE Development run

Enter `epc-frontend` folder and run `npm run serve`. Application is running on 8080 port.

### Mocking OpenAI responses

If you want to redirect the OpenAI traffic to a different endpoint (e.g. http://localhost:3001) set this variable:
```bash
export OPENAI_BASE_URL=http://localhost:3001
```
and run application in the same context (in the context where the variable is defined).
From now on all the traffic will be redirected to the selected URL

### Infrastructure setup

You can find infrastructure configuration in the `epc-infra` folder. Follow the instructions in README.md contained in
this folder to create the infrastructure

### Pushing Docker image to AWS ECR

#### Test image with local docker compose

Remember that the FE application should be built to work with a given environment (OIDC configuration requires it).
Maven profiles will be used to build applications for a dedicated environment:

- `packaged` - application is built for local docker image or JAR file
- `dev` - application is build for DEV environment (`dev-epc.finheros.com`)
- `uat` - application is build for DEV environment (`uat-epc.finacte.de`)
- `prod` - application is build for PROD environment (`fizienzpilot.finacte.de`)

First build your image using `packaged` profile (or different depending on the environment that you want to use it)
using:

```bash
TAG_NAME=packaged-`git rev-parse HEAD` && export TAG_NAME
./mvnw clean install jib:dockerBuild -Ppackaged -Dimage-tag=$TAG_NAME -Dimage-env-tag=packaged-latest -Djib.container.environment=epc.app-image-version=$TAG_NAME
```

**IMPORTANT** `packaged` profile should be used only for local environment tests.

#### Push image to ECR

In order to push your image to AWS ECR you have to be logged in to AWS ECR Docker. Please export your AWS key by running
this commands:

```bash
export AWS_ACCESS_KEY_ID=XXXXXXXXXXXXXXXXXXXXXX
export AWS_SECRET_ACCESS_KEY=XXXXXXXXXXXXXXXXXXX
export AWS_DEFAULT_REGION=eu-central-1
```

and then run:

```bash
aws ecr get-login-password | docker login -u AWS --password-stdin "https://$(aws sts get-caller-identity --query 'Account' --output text).dkr.ecr.eu-central-1.amazonaws.com"
```

Now you can push your image using JIB by running this command:

```bash
TAG_NAME=dev-`git rev-parse HEAD` && export TAG_NAME
./mvnw clean install jib:build -Pdev -Dimage-tag=$TAG_NAME -Dimage-env-tag=dev-latest  -Djib.container.environment=epc.app-image-version=$TAG_NAME

```

(you can tags to `prod-*` and `prod-latest` and profile to `prod` but be careful with that)

### Unit and Integration tests

Each Maven build process starts unit tests. Integration tests are skipped except for one test that starts the context,
database and performs migrations (the test is necessary to verify the correctness of the migration).
Test naming used by Maven plugins:

- unit tests:
  `**/Test*` [Maven Surfire] (https://maven.apache.org/surefire/maven-failsafe-plugin/examples/inclusion-exclusion.html)
- integration tests:
  `**/IT*` [Maven Failsafe] (https://maven.apache.org/surefire/maven-surefire-plugin/examples/inclusion-exclusion.html)

Command to start integration tests (**NOTE** connection to GPT required):

```bash
./mvnw failsafe:integration-test -Pintegration-test
```

Command to disable all tests:

```bash
./mvnw clean install -DskipTests
```

Currently, integration tests are not automatically run in the pipeline. It is recommended to run them locally. Please
provide `OPENAI_API_KEY` parameter for IT.

### Liquibase Configuration (migrations, rollbacks)
Our project uses Liquibase for database schema migration management. This ensures consistent and version-controlled database changes across all environments.

#### Automatic Migrations
- Migrations run automatically when the application starts
- Changes are applied to all schemas in the database
- No manual intervention required for standard deployments

#### Manual Management
- Liquibase Maven plugin is configured for manual operations. It can used to perform status checks, rollbacks and any
  other operations.

#### Rollback Support
- All changesets must include explicit rollback instructions
- Rollbacks are essential when:
    - Switching between feature branches
    - Recovering from failed migrations
    - Rolling back problematic changes in any environment
- Example command:
``` bash
  ./mvnw -pl epc-backend liquibase:rollback -Dliquibase.rollbackCount=1 -Dliquibase.defaultSchemaName=finacte 
```

#### Best Practices
1. Always test migrations locally before pushing
2. Ensure rollbacks are properly defined and tested
3. Use meaningful changeset IDs and descriptions

#### Coming Soon
- [ ] Automated GitHub Action workflow to manage rollbacks in case of deployment failures


### Connection to Remote DB

Connection is described in the [Infrastructure README.md](./epc-infra/aws/README.md)

### Authentication

Authentication and authorisation is described in [separate readme](./AUTHENTICATION_README.md)