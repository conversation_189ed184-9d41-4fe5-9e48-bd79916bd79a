package de.finacte.epc

import de.finacte.epc.configuration.PostgresContainerInitializer
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ContextConfiguration

@SpringBootTest
@ContextConfiguration(
    initializers = [PostgresContainerInitializer::class]
)
class EpcApplicationTest {

    @Test
    fun contextLoads() {
    }

}
