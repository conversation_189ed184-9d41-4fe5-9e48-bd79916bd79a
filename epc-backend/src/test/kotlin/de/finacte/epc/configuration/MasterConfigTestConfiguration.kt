package de.finacte.epc.configuration

import de.finacte.epc.utils.LoggerDelegate
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Profile
import org.springframework.jdbc.core.JdbcTemplate
import javax.annotation.PostConstruct

/**
 * Test configuration that ensures master_config schema exists and is properly set up
 * for integration tests that require multi-tenancy support.
 * 
 * This configuration can be imported into test classes that need master_config schema:
 * 
 * @SpringBootTest
 * @Import(MasterConfigTestConfiguration::class)
 * @ContextConfiguration(initializers = [PostgresContainerInitializer::class])
 * class YourIntegrationTest {
 *     // Your test code
 * }
 */
@TestConfiguration
@Profile("test")
class MasterConfigTestConfiguration {

    companion object {
        private val log by LoggerDelegate()
    }

    @Bean
    fun masterConfigSchemaInitializer(jdbcTemplate: JdbcTemplate): MasterConfigSchemaInitializer {
        return MasterConfigSchemaInitializer(jdbcTemplate)
    }

    class MasterConfigSchemaInitializer(
        private val jdbcTemplate: JdbcTemplate
    ) {

        @PostConstruct
        fun initializeMasterConfigSchema() {
            try {
                log.info("[TEST] Ensuring master_config schema exists...")
                
                // Create master_config schema if it doesn't exist
                jdbcTemplate.execute("CREATE SCHEMA IF NOT EXISTS master_config")
                
                // Create organisation table if it doesn't exist
                jdbcTemplate.execute("""
                    CREATE TABLE IF NOT EXISTS master_config.organisation (
                        id                     uuid         not null primary key,
                        name                   varchar(255) not null,
                        business_id            varchar(255) not null unique,
                        schema                 varchar(100) not null,
                        europace_client_id     varchar(255),
                        europace_client_secret varchar(255),
                        created_by             varchar(100) not null,
                        creation_date          timestamp    not null,
                        last_modified_by       varchar(100),
                        last_modified_date     timestamp
                    )
                """.trimIndent())
                
                // Create organisation_removal table if it doesn't exist
                jdbcTemplate.execute("""
                    CREATE TABLE IF NOT EXISTS master_config.organisation_removal (
                        id                     uuid         not null primary key,
                        name                   varchar(255) not null,
                        business_id            varchar(255) not null,
                        schema                 varchar(100) not null,
                        europace_client_id     varchar(255),
                        europace_client_secret varchar(255),
                        created_by             varchar(100) not null,
                        creation_date          timestamp    not null,
                        last_modified_by       varchar(100),
                        last_modified_date     timestamp,
                        removal_date           timestamp    not null
                    )
                """.trimIndent())
                
                // Insert test organisation if it doesn't exist
                val existingCount = jdbcTemplate.queryForObject(
                    "SELECT COUNT(*) FROM master_config.organisation WHERE business_id = 'TEST_ORG_001'",
                    Int::class.java
                ) ?: 0
                
                if (existingCount == 0) {
                    jdbcTemplate.execute("""
                        INSERT INTO master_config.organisation (
                            id, name, business_id, schema, europace_client_id, europace_client_secret, 
                            created_by, creation_date
                        ) VALUES (
                            gen_random_uuid(), 'Test Organisation', 'TEST_ORG_001', 'finacte',
                            'test_client_id', 'test_client_secret', 'test_config', CURRENT_TIMESTAMP
                        )
                    """.trimIndent())
                }
                
                // Create finacte schema for tenant data
                jdbcTemplate.execute("CREATE SCHEMA IF NOT EXISTS finacte")
                
                log.info("[TEST] master_config schema initialization completed successfully")
                
            } catch (e: Exception) {
                log.error("[TEST] Failed to initialize master_config schema", e)
                throw e
            }
        }
    }
}
