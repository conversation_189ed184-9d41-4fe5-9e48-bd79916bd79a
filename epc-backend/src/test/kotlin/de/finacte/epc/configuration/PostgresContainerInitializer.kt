package de.finacte.epc.configuration

import org.testcontainers.containers.BindMode
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.containers.wait.strategy.WaitStrategy
import java.time.Duration

open class PostgresContainerInitializer(
    private val persistData: Boolean = false
) : TestContainerInitializer<PostgreSQLContainer<*>>(
    "Postgres",
    singletonPostgresContainer
) {

    companion object {
        const val DATABASE_NAME = "epc"
        const val USERNAME = "test"
        const val PASSWORD = "test"
        private const val FIXED_LOCAL_PORT = 5432
        private val singletonPostgresContainer: PostgreSQLContainer<*> by lazy {
            PostgreSQLContainer("postgres:16.3")
                .apply {
                    withDatabaseName(DATABASE_NAME)
                    withUsername(USERNAME)
                    withPassword(PASSWORD)
                    setPortBindings(listOf("$FIXED_LOCAL_PORT:5432"))
                }
        }
    }

    override fun propertySourceMap(container: PostgreSQLContainer<*>): Map<String, String> {
        val jdbcUrl = "jdbc:postgresql://${container.host}:${container.firstMappedPort}/$DATABASE_NAME"
        return mapOf(
            "spring.datasource.url" to jdbcUrl,
            "spring.datasource.username" to container.username,
            "spring.datasource.password" to container.password,
            "spring.datasource.driver-class-name" to "org.postgresql.Driver"
        )
    }

    override fun applyContainerConfiguration(container: PostgreSQLContainer<*>): PostgreSQLContainer<*> {
        if (persistData) {
            container.withFileSystemBind(
                "./.local/postgres",
                "/var/lib/postgresql/data",
                BindMode.READ_WRITE
            )
        }

        // Add initialization script to create master_config schema
        container.withInitScript("test-init-master-config.sql")

        return container
    }

    override fun waitStrategy(): WaitStrategy = Wait
        .forLogMessage(".*database system is ready to accept connections.*$", 2)
        .withStartupTimeout(Duration.ofSeconds(CONTAINER_STARTUP_TIMEOUT))
}

class PersistentPostgresContainerInitializer : PostgresContainerInitializer(persistData = true)
