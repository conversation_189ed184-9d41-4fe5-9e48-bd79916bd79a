package de.finacte.epc.configuration

import org.testcontainers.containers.BindMode
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.containers.wait.strategy.WaitStrategy
import java.time.Duration

/**
 * PostgreSQL container initializer that creates the master_config schema
 * and sets up test data for multi-tenancy integration tests.
 * 
 * Use this initializer when your tests need:
 * - master_config schema with organisation data
 * - Multi-tenancy support
 * - Liquibase migrations for both master and tenant schemas
 */
class PostgresWithMasterConfigContainerInitializer(
    private val persistData: Boolean = false
) : TestContainerInitializer<PostgreSQLContainer<*>>(
    "PostgresWithMasterConfig",
    singletonPostgresContainer
) {

    companion object {
        const val DATABASE_NAME = "epc"
        const val USERNAME = "test"
        const val PASSWORD = "test"
        private const val FIXED_LOCAL_PORT = 5432
        private val singletonPostgresContainer: PostgreSQLContainer<*> by lazy {
            PostgreSQLContainer("postgres:16.3")
                .apply {
                    withDatabaseName(DATABASE_NAME)
                    withUsername(USERNAME)
                    withPassword(PASSWORD)
                    setPortBindings(listOf("$FIXED_LOCAL_PORT:5432"))
                    withInitScript("test-init-master-config.sql")
                }
        }
    }

    override fun propertySourceMap(container: PostgreSQLContainer<*>): Map<String, String> {
        val jdbcUrl = "jdbc:postgresql://${container.host}:${container.firstMappedPort}/$DATABASE_NAME"
        return mapOf(
            "spring.datasource.url" to jdbcUrl,
            "spring.datasource.username" to container.username,
            "spring.datasource.password" to container.password,
            "spring.datasource.driver-class-name" to "org.postgresql.Driver",
            // Enable master schema configuration for tests
            "epc.multitenancy.liquibase.master.defaultSchema" to "master_config"
        )
    }

    override fun applyContainerConfiguration(container: PostgreSQLContainer<*>): PostgreSQLContainer<*> {
        if (persistData) {
            container.withFileSystemBind(
                "./.local/postgres",
                "/var/lib/postgresql/data",
                BindMode.READ_WRITE
            )
        }
        return container
    }

    override fun waitStrategy(): WaitStrategy = Wait
        .forLogMessage(".*database system is ready to accept connections.*$", 2)
        .withStartupTimeout(Duration.ofSeconds(CONTAINER_STARTUP_TIMEOUT))
}

/**
 * Persistent version of the PostgreSQL container with master_config schema
 */
class PersistentPostgresWithMasterConfigContainerInitializer : 
    PostgresWithMasterConfigContainerInitializer(persistData = true)
