-- Test initialization script for PostgreSQL test container
-- Creates master_config schema and basic test data for integration tests

-- Create master_config schema
CREATE SCHEMA IF NOT EXISTS master_config;

-- Create test organisation table in master_config schema
-- This mirrors the structure from the production migration
CREATE TABLE IF NOT EXISTS master_config.organisation (
    id                     uuid         not null primary key,
    name                   varchar(255) not null,
    business_id            varchar(255) not null unique,
    schema                 varchar(100) not null,
    europace_client_id     varchar(255),
    europace_client_secret varchar(255),
    created_by             varchar(100) not null,
    creation_date          timestamp    not null,
    last_modified_by       varchar(100),
    last_modified_date     timestamp
);

-- Create test organisation_removal table in master_config schema
CREATE TABLE IF NOT EXISTS master_config.organisation_removal (
    id                     uuid         not null primary key,
    name                   varchar(255) not null,
    business_id            varchar(255) not null,
    schema                 varchar(100) not null,
    europace_client_id     varchar(255),
    europace_client_secret varchar(255),
    created_by             var<PERSON><PERSON>(100) not null,
    creation_date          timestamp    not null,
    last_modified_by       varchar(100),
    last_modified_date     timestamp,
    removal_date           timestamp    not null
);

-- Insert test organisation data
-- This creates a test organisation that uses the 'finacte' schema (matching TestUserTenantIdentifierResolver)
INSERT INTO master_config.organisation (
    id, 
    name, 
    business_id, 
    schema, 
    europace_client_id, 
    europace_client_secret, 
    created_by, 
    creation_date
) VALUES (
    gen_random_uuid(),
    'Test Organisation',
    'TEST_ORG_001',
    'finacte',
    'test_client_id',
    'test_client_secret',
    'test_init_script',
    CURRENT_TIMESTAMP
) ON CONFLICT (business_id) DO NOTHING;

-- Create the finacte schema for tenant data
CREATE SCHEMA IF NOT EXISTS finacte;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA master_config TO test;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA master_config TO test;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA master_config TO test;

GRANT USAGE ON SCHEMA finacte TO test;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA finacte TO test;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA finacte TO test;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA master_config GRANT ALL PRIVILEGES ON TABLES TO test;
ALTER DEFAULT PRIVILEGES IN SCHEMA master_config GRANT ALL PRIVILEGES ON SEQUENCES TO test;

ALTER DEFAULT PRIVILEGES IN SCHEMA finacte GRANT ALL PRIVILEGES ON TABLES TO test;
ALTER DEFAULT PRIVILEGES IN SCHEMA finacte GRANT ALL PRIVILEGES ON SEQUENCES TO test;
