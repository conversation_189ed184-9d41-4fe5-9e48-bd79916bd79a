package de.finacte.epc.config.multitenancy

import de.finacte.epc.config.repository.OrganisationRepository
import liquibase.integration.spring.SpringLiquibase
import org.springframework.boot.autoconfigure.liquibase.LiquibaseProperties
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import javax.sql.DataSource


@Configuration
@EnableConfigurationProperties(LiquibaseProperties::class)
class LiquibaseConfig {

    @Bean
    @ConfigurationProperties("epc.multitenancy.liquibase.master")
    fun masterLiquibaseProperties(): LiquibaseProperties {
        return LiquibaseProperties()
    }

    @Bean
    @ConfigurationProperties("epc.multitenancy.liquibase.tenant")
    fun tenantLiquibaseProperties(): LiquibaseProperties {
        return LiquibaseProperties()
    }

    @Bean
    fun tenantLiquibase(
        masterOrganisationRepository: OrganisationRepository,
        dataSource: DataSource
    ): DynamicSchemaBasedMultiTenantSpringLiquibase {
        val liquibaseProperties = tenantLiquibaseProperties()
        return DynamicSchemaBasedMultiTenantSpringLiquibase(masterOrganisationRepository, dataSource, liquibaseProperties)
    }

    @Bean
    fun masterLiquibase(dataSource: DataSource): SpringLiquibase {
        val liquibaseProperties = masterLiquibaseProperties()
        val liquibase = SpringLiquibase()
        liquibase.dataSource = dataSource
        liquibase.changeLog = liquibaseProperties.changeLog
        liquibase.defaultSchema = liquibaseProperties.defaultSchema
        return liquibase
    }
}