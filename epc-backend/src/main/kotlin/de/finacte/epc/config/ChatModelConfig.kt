package de.finacte.epc.config

import org.springframework.ai.openai.OpenAiChatModel
import org.springframework.ai.openai.OpenAiChatOptions
import org.springframework.ai.openai.api.OpenAiApi
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary

@ConfigurationProperties(prefix = "spring.ai.openai")
data class OpenAIModelConfigProperties(
    val apiKey: String,
    val baseUrl: String? = null
)

@Configuration
@EnableConfigurationProperties(OpenAIModelConfigProperties::class)
class ChatModelConfig(
    private val config: OpenAIModelConfigProperties
) {

    @Bean(name = ["openAi4oMiniChatModel"])
    @Primary
    fun openAi4oMiniChatModel(): OpenAiChatModel = createChatModel("gpt-4o-mini", 0.7)

    @Bean(name = ["openAi41ChatModel"])
    fun openAi41ChatModel(): OpenAiChatModel = createChatModel("gpt-4.1", 0.0)

    private fun createChatModel(model: String, temperature: Double): OpenAiChatModel {
        val openAiApi = OpenAiApi.builder().apply {
            apiKey(config.apiKey)
            config.baseUrl?.takeIf { it.isNotBlank() }?.let { baseUrl(it) }
        }.build()

        val options = OpenAiChatOptions.builder()
            .model(model)
            .temperature(temperature)
            .build()

        return OpenAiChatModel.builder()
            .openAiApi(openAiApi)
            .defaultOptions(options)
            .build()
    }
}

