package de.finacte.epc.config.multitenancy

import de.finacte.epc.config.createCommonJpaProperties
import jakarta.persistence.EntityManagerFactory
import org.hibernate.cfg.AvailableSettings
import org.hibernate.context.spi.CurrentTenantIdentifierResolver
import org.hibernate.engine.jdbc.connections.spi.MultiTenantConnectionProvider
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.jpa.repository.config.EnableJpaAuditing
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.orm.jpa.JpaTransactionManager
import org.springframework.orm.jpa.JpaVendorAdapter
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter

@Configuration
@EnableJpaAuditing
@EnableJpaRepositories(
    basePackages = ["de.finacte.epc.repository"],
    entityManagerFactoryRef = "tenantEntityManagerFactory",
    transactionManagerRef = "tenantTransactionManager"
)
class TenantPersistenceConfig @Autowired constructor(
    private val beanFactory: ConfigurableListableBeanFactory,
    private val jpaProperties: JpaProperties
) {
    companion object {
        private const val TENANT_ENTITY_PACKAGE: String = "de.finacte.epc.entity"
    }

    @Bean
    fun tenantEntityManagerFactory(
        connectionProvider: MultiTenantConnectionProvider<String>,
        tenantResolver: CurrentTenantIdentifierResolver<String?>
    ): LocalContainerEntityManagerFactoryBean {
        val emfBean = LocalContainerEntityManagerFactoryBean()
        emfBean.persistenceUnitName = "tenant-persistence-unit"
        emfBean.setPackagesToScan(TENANT_ENTITY_PACKAGE)

        val vendorAdapter: JpaVendorAdapter = HibernateJpaVendorAdapter()
        emfBean.jpaVendorAdapter = vendorAdapter

        val properties: MutableMap<String, Any?> = createCommonJpaProperties(jpaProperties, beanFactory)

        properties[AvailableSettings.MULTI_TENANT_CONNECTION_PROVIDER] = connectionProvider
        properties[AvailableSettings.MULTI_TENANT_IDENTIFIER_RESOLVER] = tenantResolver
        properties.remove(AvailableSettings.DEFAULT_SCHEMA)

        emfBean.setJpaPropertyMap(properties)
        return emfBean
    }

    @Bean
    fun tenantTransactionManager(
        @Qualifier("tenantEntityManagerFactory") emf: EntityManagerFactory?
    ): JpaTransactionManager {
        val tenantTransactionManager = JpaTransactionManager()
        tenantTransactionManager.entityManagerFactory = emf
        return tenantTransactionManager
    }


}