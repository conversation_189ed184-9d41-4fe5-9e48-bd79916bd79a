package de.finacte.epc.config.multitenancy

import de.finacte.epc.config.PUBLIC_SCHEMA
import de.finacte.epc.exception.multitenancy.EPCMultitenancyException
import de.finacte.epc.utils.LoggerDelegate
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.stereotype.Service

@Service
class KeycloakJwtOrganisationResolver : OrganisationResolver {
    companion object {
        private val log by LoggerDelegate()
    }

    override fun getOrganisationBusinessId(principal: Jwt): String {
        val organisation = principal.claims["organization"]
            ?: run {
                log.debug("Organization claim not found for principal ${principal.subject}, using default schema: $PUBLIC_SCHEMA")
                mutableListOf(PUBLIC_SCHEMA)
            }
        if (organisation is ArrayList<*> && organisation.first() is String) {
            return organisation.first() as String
        } else throw EPCMultitenancyException("`organization` claim is not of type ArrayList<String> for principal ${principal.subject}")
    }
}