package de.finacte.epc.config

import org.hibernate.cfg.AvailableSettings
import org.hibernate.cfg.MappingSettings
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties
import org.springframework.orm.hibernate5.SpringBeanContainer

const val MASTER_CONFIG_SCHEMA = "master_config"
const val PUBLIC_SCHEMA = "public"

fun createCommonJpaProperties(
    jpaProperties: JpaProperties,
    beanFactory: ConfigurableListableBeanFactory
): MutableMap<String, Any?> {
    val properties: MutableMap<String, Any?> = HashMap(jpaProperties.properties)
    properties[AvailableSettings.BEAN_CONTAINER] = SpringBeanContainer(beanFactory)
    properties[MappingSettings.PHYSICAL_NAMING_STRATEGY] =
        "org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy"
    properties[MappingSettings.IMPLICIT_NAMING_STRATEGY] =
        "org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy"
    return properties
}
