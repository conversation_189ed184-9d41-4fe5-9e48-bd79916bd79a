package de.finacte.epc.config.multitenancy

import de.finacte.epc.config.MASTER_CONFIG_SCHEMA
import de.finacte.epc.config.PUBLIC_SCHEMA
import de.finacte.epc.config.repository.OrganisationRepository
import de.finacte.epc.exception.multitenancy.EPCMultitenancyException
import de.finacte.epc.utils.LoggerDelegate
import org.hibernate.context.spi.CurrentTenantIdentifierResolver
import org.springframework.context.annotation.Profile
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.stereotype.Component

@Profile("!no-security")
@Component
class UserTenantIdentifierResolver(
    private val masterOrganisationRepository: OrganisationRepository,
    private val organisationResolver: OrganisationResolver,
) : CurrentTenantIdentifierResolver<String> {
    companion object {
        private val log by LoggerDelegate()
    }

    override fun resolveCurrentTenantIdentifier(): String {
        val principal = SecurityContextHolder.getContext().authentication?.principal

        val (username, organisation, schema) = if (principal != null && principal is Jwt) {
            val orgBusinessId = organisationResolver.getOrganisationBusinessId(principal)
            val schema = if(orgBusinessId == PUBLIC_SCHEMA) PUBLIC_SCHEMA else masterOrganisationRepository.findSchemaByBusinessId(orgBusinessId)
                ?: throw EPCMultitenancyException("Can't find schema for organisation business ID: $orgBusinessId ")
            Triple(principal.subject, orgBusinessId, schema)
        } else {
            Triple(null, null, null)
        }
        log.debug(
            "[Multitenancy] Lookup for tenant schema during DB call. Principal username: {}, principal organisation: {}, picked schema: {}",
            username,
            organisation,
            schema
        )
        return schema ?: MASTER_CONFIG_SCHEMA
    }

    override fun validateExistingCurrentSessions(): Boolean {
        return true
    }
}