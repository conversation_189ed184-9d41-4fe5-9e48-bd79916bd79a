server:
  port: 8098
#  Below it's a fix for IllegalArgumentException triggered when incorrect request is sent
  tomcat:
    relaxed-path-chars:
      - '<'
      - '>'
      - '['
      - '\'
      - ']'
      - '^'
      - '`'
      - '{'
      - '|'
      - '}'
      - ':'
    relaxed-query-chars:
      - '<'
      - '>'
      - '['
      - '\'
      - ']'
      - '^'
      - '`'
      - '{'
      - '|'
      - '}'
      - ':'
spring:
  application:
    name: Finacte EPC
  devtools:
    livereload:
      enabled: false
  datasource:
    url: ************************************
    username: epc
    password: asdf5^F23
    driver-class-name: org.postgresql.Driver
    hikari:
      maximumPoolSize: 20 # default is 10
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: none
    open-in-view: false
  ai:
    chat:
      client:
        enabled: false
    openai:
      api-key: ${OPENAI_API_KEY}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: https://dev-epc.finheros.com/auth/realms/Effizienzpilot/protocol/openid-connect/certs

epc:
  app-image-version: replace
  cache-manager:
    caffeine-cache-configs:
      - cache-names: [ ORGANISATION_SCHEMA ]
        spec: initialCapacity=10, maximumSize=100, expireAfterAccess=100m
      - cache-names: [ CONSTRUCTION_MATERIAL_PROPERTIES ]
        spec: initialCapacity=50, maximumSize=100, expireAfterAccess=100m
  europace-api:
    oauth-url: https://api.europace.de/auth/token
    api-base-url: https://baufinanzierung.api.europace.de
    datenkontext: TEST_MODUS
  calculation:
    calculation-service: V1
    electricity-consumption:
      single-tenant-household-avg-consumption-per-year-kwh: 1750.0
      tenant-avg-consumption-per-year-kwh: 1000.0

  multitenancy:
    organisation-removal-cron: "0 0 1 * * *"
    liquibase:
      master:
        changeLog: classpath:db/changelog/db.changelog-master.yaml
        defaultSchema: master_config
      tenant:
        changeLog: classpath:db/changelog/db.changelog-tenant.yaml

logging:
  pattern:
    console: "%clr(%d{${LOG_DATEFORMAT_PATTERN:yyyy-MM-dd'T'HH:mm:ss.SSSXXX}}){faint} %clr(${LOG_LEVEL_PATTERN:%5p}) %clr(${PID: }){magenta} %clr(---){faint} %clr([%X]) %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:%wEx}"
    file: "%d{${LOG_DATEFORMAT_PATTERN:yyyy-MM-dd'T'HH:mm:ss.SSSXXX}} ${LOG_LEVEL_PATTERN:%5p} ${PID: } --- [%X] [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:%wEx}"
