-- master_config schema must exist before running this migration (egg, chicken problem).
-- You can create it manually or using create-required-schemas-lambda in infra code
-- CREATE SCHEMA IF NOT EXISTS master_config;

--in case this is initial migration on empty environment. Existing environment would have this table
create table if not exists public.organisation
(
    id                     uuid         not null
        primary key,
    name                   varchar(255) not null,
    business_id            varchar(255) not null
        unique,
    schema                 varchar(100) not null,
    europace_client_id     varchar(255),
    europace_client_secret varchar(255),
    created_by             varchar(100) not null,
    creation_date          timestamp    not null,
    last_modified_by       varchar(100),
    last_modified_date     timestamp
);

--in case this is initial migration on empty environment. Existing environment would have this table
create table if not exists public.organisation_removal
(
    id                 uuid         not null
        primary key,
    name               varchar(255) not null,
    schema             varchar(100) not null,
    business_id        varchar(255) not null,
    completed          boolean      not null,
    created_by         varchar(100) not null,
    creation_date      timestamp    not null,
    last_modified_by   varchar(100),
    last_modified_date timestamp
);


create table master_config.organisation (like public.organisation including all);
create table master_config.organisation_removal (like public.organisation_removal including all);

insert into master_config.organisation
select *
from public.organisation;

insert into master_config.organisation_removal
select *
from public.organisation_removal;

drop table public.organisation;
drop table public.organisation_removal;
drop table if exists public.databasechangelog;
drop table if exists public.databasechangeloglock;