# Master Config Schema in PostgreSQL Test Container

This document explains how to use the `master_config` schema in PostgreSQL test containers for integration tests that require multi-tenancy support.

## Overview

The application uses a multi-tenant architecture with schema-based separation:
- `master_config` schema: Contains organisation metadata and configuration
- Tenant schemas (e.g., `finacte`): Contains tenant-specific data

## Available Approaches

### 1. Modified PostgresContainerInitializer (Default)

The standard `PostgresContainerInitializer` has been enhanced to automatically create the `master_config` schema using an initialization script.

**Usage:**
```kotlin
@SpringBootTest
@ContextConfiguration(
    initializers = [PostgresContainerInitializer::class]
)
class YourTest {
    // Your test code
}
```

**Features:**
- Automatically creates `master_config` schema
- Creates `organisation` and `organisation_removal` tables
- Inserts test organisation data
- Creates `finacte` schema for tenant data
- Sets up proper permissions

### 2. PostgresWithMasterConfigContainerInitializer (Specialized)

A specialized container initializer specifically designed for tests requiring master_config schema.

**Usage:**
```kotlin
@SpringBootTest
@ContextConfiguration(
    initializers = [PostgresWithMasterConfigContainerInitializer::class]
)
class YourMultiTenancyTest {
    // Your test code
}
```

**Features:**
- Same as approach 1, but more explicit
- Includes master schema configuration in property source
- Better for tests that specifically need multi-tenancy

### 3. MasterConfigTestConfiguration (Programmatic)

A test configuration that programmatically ensures the master_config schema exists.

**Usage:**
```kotlin
@SpringBootTest
@Import(MasterConfigTestConfiguration::class)
@ContextConfiguration(
    initializers = [PostgresContainerInitializer::class]
)
class YourTest {
    // Your test code
}
```

**Features:**
- Creates schema and tables programmatically
- Can be combined with any container initializer
- More flexible for custom test scenarios

## Test Data

All approaches create the following test data:

### Organisation Table
- **ID**: Auto-generated UUID
- **Name**: "Test Organisation"
- **Business ID**: "TEST_ORG_001"
- **Schema**: "finacte" (matches `TestUserTenantIdentifierResolver.TEST_SCHEMA`)
- **Europace Client ID**: "test_client_id"
- **Europace Client Secret**: "test_client_secret"

### Schemas Created
- `master_config`: For organisation metadata
- `finacte`: For tenant data (matches test configuration)

## Example Test

See `MasterConfigSchemaTest.kt` for a complete example that demonstrates:
- Verifying schema existence
- Querying organisation data
- Testing multi-tenancy setup

## When to Use Each Approach

### Use PostgresContainerInitializer (Approach 1) when:
- You want the simplest setup
- Your tests might occasionally need master_config but it's not the main focus
- You want a single container initializer for all tests

### Use PostgresWithMasterConfigContainerInitializer (Approach 2) when:
- Your tests specifically focus on multi-tenancy features
- You want to be explicit about requiring master_config schema
- You need the master schema configuration in Spring properties

### Use MasterConfigTestConfiguration (Approach 3) when:
- You need custom schema setup logic
- You want to combine with existing container initializers
- You need more control over the initialization process

## Liquibase Integration

The master_config schema works with the existing Liquibase configuration:

```yaml
epc:
  multitenancy:
    liquibase:
      master:
        changeLog: classpath:db/changelog/db.changelog-master.yaml
        defaultSchema: master_config
      tenant:
        changeLog: classpath:db/changelog/db.changelog-tenant.yaml
```

Master migrations will run against the `master_config` schema, while tenant migrations will run against tenant schemas like `finacte`.

## Troubleshooting

### Schema Not Found
If you get "schema does not exist" errors:
1. Ensure you're using one of the approaches above
2. Check that the initialization script is being loaded
3. Verify the container startup logs for any errors

### Permission Denied
If you get permission errors:
1. The initialization script sets up proper permissions for the `test` user
2. Ensure you're using the correct database credentials
3. Check that the schema creation completed successfully

### Liquibase Errors
If Liquibase fails to run:
1. Ensure the master_config schema exists before Liquibase runs
2. Check the Liquibase configuration in your test properties
3. Verify that the master changelog files are accessible
