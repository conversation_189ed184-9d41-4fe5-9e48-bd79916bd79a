import * as cdk from "aws-cdk-lib";
import {Duration} from "aws-cdk-lib";
import {Construct} from 'constructs';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as ec2 from "aws-cdk-lib/aws-ec2";
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as nodeJsLambda from 'aws-cdk-lib/aws-lambda-nodejs';
import * as cr from 'aws-cdk-lib/custom-resources';
import {SecurityGroup} from "aws-cdk-lib/aws-ec2";
import path = require("path");


interface KeycloakSchemaEcsTaskStackProps extends cdk.NestedStackProps {
    readonly envName: string;
    readonly dbInstance: rds.DatabaseInstance;
    readonly dbName: string;
    readonly environmentVpc: ec2.Vpc;
    readonly bastionHostSecurityGroup: SecurityGroup
}


export class KeycloakSchemaLambdaStack extends cdk.NestedStack {
    public keycloakDbUserSecretArn: string;

    constructor(scope: Construct, id: string, props: KeycloakSchemaEcsTaskStackProps) {
        super(scope, id, props);
        const keycloakSchemaUsername = 'keycloak';

        const keycloakDbUserSecret = new secretsmanager.Secret(this, `${props.envName}-epc-keycloak-schema-secret`, {
            secretName: `${props.envName}-epc-keycloak-schema-secret`,
            description: `A secret for Keycloak DB schema for ${props.envName} environment`,
            generateSecretString: {
                secretStringTemplate: JSON.stringify({username: keycloakSchemaUsername}),
                generateStringKey: 'password',
                excludeCharacters: '"@/\\',
            },
        });
        this.keycloakDbUserSecretArn = keycloakDbUserSecret.secretArn;

        const lambdaFunction = new nodeJsLambda.NodejsFunction(this, `${props.envName}-epc-keycloak-schema-lambda`, {
            runtime: lambda.Runtime.NODEJS_18_X,
            handler: 'index.handler',
            entry: path.join(__dirname, '../../keycloak-create-schema-lambda/index.js'),
            depsLockFilePath: path.join(__dirname, '../../keycloak-create-schema-lambda/package-lock.json'),
            environment: {
                DB_HOST: props.dbInstance.secret?.secretValueFromJson('host').unsafeUnwrap() || '',
                DB_USER: props.dbInstance.secret?.secretValueFromJson('username').unsafeUnwrap() || '',
                DB_PASS: props.dbInstance.secret?.secretValueFromJson('password').unsafeUnwrap() || '',
                DB_NAME: props.dbName,
                KEYCLOAK_DB_USERNAME: keycloakDbUserSecret.secretValueFromJson("username").unsafeUnwrap(),
                KEYCLOAK_DB_PASSWORD: keycloakDbUserSecret.secretValueFromJson("password").unsafeUnwrap(),
            },
            vpc: props.environmentVpc,
            securityGroups: [props.bastionHostSecurityGroup],
            bundling: {
                nodeModules: [
                    "pg"
                ],
            },
            timeout: Duration.minutes(1)
        });

        const invokeLambdaResource = new cr.AwsCustomResource(this, `${props.envName}-epc-keycloak-schema-lambda-invoke`, {
            onCreate: {
                service: 'Lambda',
                action: 'invoke',
                parameters: {
                    FunctionName: lambdaFunction.functionName
                },
                physicalResourceId: cr.PhysicalResourceId.of(`${props.envName}-epc-keycloak-schema-lambda-invoke-resource`),
            },
            policy: cr.AwsCustomResourcePolicy.fromSdkCalls({
                resources: [lambdaFunction.functionArn],
            }),
        });

        lambdaFunction.grantInvoke(invokeLambdaResource.grantPrincipal);

    }
}