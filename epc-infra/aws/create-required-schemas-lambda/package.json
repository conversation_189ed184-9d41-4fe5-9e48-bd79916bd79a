{"name": "keycloak-create-schema-lambda", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "esbuild --bundle --minify --platform=node --target=node18 --outdir=build index.js  --external:@aws-sdk/* --external:pg ", "export": "cd build && zip index.js.zip index.js"}, "repository": {"type": "git", "url": "https://github.com/Finacte/finacte-epc.git"}, "private": true, "devDependencies": {"esbuild": "^0.24.2", "pg": "^8.13.1"}}